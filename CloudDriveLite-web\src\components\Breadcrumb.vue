<template>
  <el-breadcrumb :separator-icon="ArrowRight">
    <!-- 首页固定 -->
    <el-breadcrumb-item :to="{ path: '/' }">首页</el-breadcrumb-item>

    <!-- 后端给的层级 -->
    <el-breadcrumb-item
      v-for="(item, index) in folderChain"
      :key="item.id"
      @click="handleBreadcrumbClick(index)"
    > 
      {{ item.name }}
    </el-breadcrumb-item>
    
    <!-- 调试信息
    <div style="font-size: 10px; color: #999; margin-top: 4px;">
      调试: {{ folderChain.map(item => item.name).join(' > ') }}
    </div> -->
  </el-breadcrumb>
</template>

<script setup lang="ts">
import { toRef } from 'vue'
import { ArrowRight } from '@element-plus/icons-vue'
import { useFileSystemStore } from '@/stores/fileSystem'

/* 接受父级传进来的链条 */
const props = defineProps<{
  folderChain: { id: string | number; name: string }[]
}>()
const folderChain = toRef(props, 'folderChain')

const { navigateToPath } = useFileSystemStore()

// 处理面包屑点击
function handleBreadcrumbClick(index: number) {
  navigateToPath(index)
}
</script>

<style scoped>
.el-breadcrumb {
  font-size: 14px;
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner {
  color: #3b82f6;
  font-weight: 600;
}

.el-breadcrumb__item .el-breadcrumb__inner {
  color: #64748b;
  transition: color 0.2s ease;
}

.el-breadcrumb__item .el-breadcrumb__inner:hover {
  color: #3b82f6;
}
</style>
