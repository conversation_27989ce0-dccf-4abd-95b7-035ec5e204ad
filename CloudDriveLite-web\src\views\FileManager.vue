<template>
  <el-container style="height: 100vh">
    
    <el-main style="padding: 20px;">
      <el-card shadow="never">
        <div style="margin-bottom: 12px; display: flex; gap: 8px; align-items: center;">
          <Back :disabled="folderChain.length <= 1" @back="goBack" />
          <span style="color: var(--el-text-color-secondary)">当前路径：{{ currentPath }}</span>
          <Breadcrumb :folder-chain="folderChain" />
        </div>
        <div class="table-title">
          <span>文件名</span>
          <span>修改时间</span>
          <span>大小</span>
        </div>
        <el-table :data="tableData" border stripe style="width: 100%">
          <el-table-column type="selection" width="48" />
          <el-table-column label="文件名" min-width="300">
            <template #default="{ row }">
              <el-icon style="margin-right: 6px">
                <Folder v-if="row.folder" />
                <Picture v-else-if="row.fileType && row.fileType.startsWith('image/')" />
                <VideoPlay v-else-if="row.fileType && row.fileType.startsWith('video/')" />
                <Document v-else />
              </el-icon>
              <span 
                class="file-name" 
                :class="{ 'folder-name': row.folder }"
                @dblclick="handleItemDoubleClick(row)"
              >
                {{ row.name }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="updatedAt" label="修改时间" width="200" />
          <el-table-column prop="sizeText" label="大小" width="140" />
        </el-table>
        <div class="table-footer">
          <span>共 {{ total }} 项</span>
          <el-pagination layout="prev, pager, next" :total="total" :page-size="size" :current-page="page" @current-change="onPageChange" small />
        </div>
      </el-card>
    </el-main>
  </el-container>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { Cloudy, Folder, Refresh, Setting, Document, Picture, VideoPlay } from '@element-plus/icons-vue'
import Back from '@/components/Back.vue'
import Breadcrumb from '@/components/Breadcrumb.vue'
import { listFiles, type FileItem as ApiFileItem } from '@/api/files'
import { ElMessage } from 'element-plus'

type Row = {
  id: number
  name: string
  folder: boolean
  fileType: string
  sizeText: string
  updatedAt: string
}

const tableData = ref<Row[]>([])
const page = ref(1)
const size = ref(20)
const total = ref(0)
const folderId = ref<number>(0)
const pathStack = ref<number[]>([0])

type Crumb = { id: number; name: string }
const folderChain = ref<Crumb[]>([{ id: 0, name: '/' }])
const currentPath = ref<string>('/')

async function load() {
  try {
    console.log('开始加载文件列表，folderId:', folderId.value)
    const data = await listFiles({ folderId: folderId.value, page: page.value, size: size.value })
    console.log('后端返回数据:', data)
    total.value = data.total
    tableData.value = data.items.map((it: ApiFileItem) => ({
      id: it.id,
      name: it.fileName,
      folder: Boolean(it.folder),
      fileType: it.fileType,
      sizeText: it.fileSizeFormatted,
      updatedAt: it.uploadedTime,
    }))
    console.log('转换后的表格数据:', tableData.value)
  } catch (e: any) {
    console.error('加载失败:', e)
    ElMessage.error(e.message || '加载失败')
  }
}

// 处理项目双击
function handleItemDoubleClick(row: Row) {
  if (row.folder) {
    pathStack.value.push(row.id)
    folderId.value = row.id
    page.value = 1
    // 简单路径展示，可替换为后端breadcrumb
    folderChain.value.push({ id: row.id, name: row.name })
    currentPath.value = '/' + folderChain.value.slice(1).map(c => c.name).join('/')
    load()
  } else {
    // 文件后续处理：预览/下载
  }
}

// 返回上级文件夹
function goBack() {
  if (pathStack.value.length <= 1) return
  pathStack.value.pop()
  folderChain.value.pop()
  folderId.value = pathStack.value[pathStack.value.length - 1]
  currentPath.value = '/' + folderChain.value.slice(1).map(c => c.name).join('/')
  page.value = 1
  load()
}

// 分页变化
function onPageChange(p: number) {
  page.value = p
  load()
}

// 加载文件列表
onMounted(() => {
  load()
})
</script>

<style scoped>
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid var(--el-border-color-lighter);
}
.brand {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}
.title { font-size: 14px; }
.nav-actions { display: flex; align-items: center; gap: 8px; }
.table-title { display: none; }
.file-name { 
  cursor: pointer; 
  color: var(--el-text-color-primary);
  transition: color 0.2s ease;
}

.file-name:hover {
  color: #3b82f6;
}

.folder-name {
  font-weight: 500;
  color: #1e293b;
}

.folder-name:hover {
  color: #3b82f6;
}
.table-footer { display: flex; justify-content: space-between; padding: 12px 0 0; }
</style>


