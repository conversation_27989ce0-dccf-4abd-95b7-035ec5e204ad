spring.application.name=learnSpringboot
#配置当前应用服务端口 默认8080
server.port=8080

spring.datasource.url=*************************************************************
spring.datasource.username=root
spring.datasource.password=331307
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

spring.jpa.database-platform=org.hibernate.dialect.MySQLDialect
spring.jpa.show-sql=true
spring.jpa.hibernate.ddl-auto=update

