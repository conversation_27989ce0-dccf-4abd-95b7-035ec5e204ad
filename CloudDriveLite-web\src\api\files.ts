export type FileItem = {
  id: number
  parentId: number
  fileName: string
  fileType: string
  folder: boolean
  fileSize: number
  fileSizeFormatted: string
  uploadedTime: string
  downloadUrl: string
  previewUrl: string
  isImage: boolean
}

export type PageResp<T> = {
  items: T[]
  page: number
  size: number
  total: number
}

export async function listFiles(params: { folderId?: number; page?: number; size?: number }) {
  const q = new URLSearchParams()
  if (params.folderId != null) q.set('folderId', String(params.folderId))
  if (params.page != null) q.set('page', String(params.page))
  if (params.size != null) q.set('size', String(params.size))
  const res = await fetch(`/api/files?${q.toString()}`, { credentials: 'include' })
  const json = await res.json()
  if (!json.success) throw new Error(json.message || '加载失败')
  
  // 转换后端格式到前端期望格式
  const data = json.data
  return {
    items: data.items || [],
    page: data.page || 1,
    size: data.size || 20,
    total: data.total || 0
  } as PageResp<FileItem>
}


